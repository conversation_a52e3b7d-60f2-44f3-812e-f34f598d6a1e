#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AdsPower Browser简单自动化脚本
基于截图坐标的直接操作
"""

import pyautogui as pg
import time

# 禁用pyautogui的安全机制
pg.FAILSAFE = False
pg.PAUSE = 0.5

def create_browser_simple(browser_name: str = "1"):
    """简单的创建浏览器操作"""
    print("🚀 开始自动化创建AdsPower浏览器...")
    print("=" * 50)
    
    try:
        # 步骤1: 点击新建浏览器按钮
        print("📍 步骤1: 点击新建浏览器按钮")
        print("坐标: (102, 82)")
        pg.click(102, 82)
        print("✅ 已点击新建浏览器按钮")
        time.sleep(2)  # 等待对话框打开
        
        # 步骤2: 点击名称输入框
        print("\n📍 步骤2: 点击名称输入框")
        print("坐标: (563, 177)")
        pg.click(563, 177)
        print("✅ 已点击名称输入框")
        time.sleep(0.5)
        
        # 步骤3: 清空并输入名称
        print(f"\n📍 步骤3: 输入浏览器名称 '{browser_name}'")
        pg.hotkey('ctrl', 'a')  # 全选现有内容
        time.sleep(0.2)
        pg.typewrite(browser_name, interval=0.1)  # 输入新名称
        print(f"✅ 已输入浏览器名称: '{browser_name}'")
        time.sleep(0.5)
        
        # 步骤4: 点击确定按钮
        print("\n📍 步骤4: 点击确定按钮")
        print("坐标: (379, 603)")
        pg.click(379, 603)
        print("✅ 已点击确定按钮")
        
        print("\n🎉 自动化创建浏览器完成!")
        print("=" * 50)
        return True
        
    except Exception as e:
        print(f"\n❌ 操作过程中出现错误: {e}")
        return False

def create_browser_with_delay(browser_name: str = "1", delay: int = 3):
    """带延迟的创建浏览器操作，给用户准备时间"""
    print("🚀 AdsPower Browser 自动化脚本")
    print("=" * 50)
    print("请确保:")
    print("1. AdsPower Browser 已经打开")
    print("2. 程序界面可见且未被其他窗口遮挡")
    print("3. 鼠标不要移动到程序界面上")
    print("=" * 50)
    
    for i in range(delay, 0, -1):
        print(f"⏰ {i} 秒后开始自动化操作...")
        time.sleep(1)
    
    print("\n🚀 开始执行自动化操作!")
    return create_browser_simple(browser_name)

def show_coordinates():
    """显示鼠标当前坐标（调试用）"""
    print("🔍 坐标获取模式")
    print("移动鼠标到目标位置，按 Ctrl+C 停止")
    print("-" * 30)
    
    try:
        while True:
            x, y = pg.position()
            print(f"\r当前坐标: ({x}, {y})", end="", flush=True)
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n坐标获取结束")

def main():
    """主函数"""
    print("AdsPower Browser 自动化工具")
    print("=" * 40)
    
    while True:
        print("\n请选择操作:")
        print("1. 立即执行自动化（适合已准备好的情况）")
        print("2. 延迟执行自动化（给3秒准备时间）")
        print("3. 获取鼠标坐标（调试用）")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            browser_name = input("请输入浏览器名称 (默认为 '1'): ").strip()
            if not browser_name:
                browser_name = "1"
            create_browser_simple(browser_name)
            
        elif choice == "2":
            browser_name = input("请输入浏览器名称 (默认为 '1'): ").strip()
            if not browser_name:
                browser_name = "1"
            create_browser_with_delay(browser_name, 3)
            
        elif choice == "3":
            show_coordinates()
            
        elif choice == "4":
            print("再见!")
            break
            
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
