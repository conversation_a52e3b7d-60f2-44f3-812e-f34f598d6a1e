#!/usr/bin/env python3
"""
Test script to verify MCP server can start correctly
"""
import sys
import os
import subprocess

def test_mcp_server():
    """Test if the MCP server can start"""
    print("Testing MCP server startup...")
    
    # Test the exact command that would be used in the MCP configuration
    python_exe = r"Y:\Windows-MCP-main\.venv\Scripts\python.exe"
    main_py = r"Y:\Windows-MCP-main\main.py"
    cwd = r"Y:\Windows-MCP-main"
    
    print(f"Python executable: {python_exe}")
    print(f"Main script: {main_py}")
    print(f"Working directory: {cwd}")
    
    # Check if files exist
    if not os.path.exists(python_exe):
        print(f"❌ Python executable not found: {python_exe}")
        return False
        
    if not os.path.exists(main_py):
        print(f"❌ Main script not found: {main_py}")
        return False
        
    if not os.path.exists(cwd):
        print(f"❌ Working directory not found: {cwd}")
        return False
    
    print("✅ All files exist")
    
    # Test the command
    try:
        print("Starting MCP server test...")
        process = subprocess.Popen(
            [python_exe, main_py],
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for startup
        import time
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ MCP server started successfully")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ MCP server failed to start")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting MCP server: {e}")
        return False

if __name__ == "__main__":
    success = test_mcp_server()
    sys.exit(0 if success else 1)
