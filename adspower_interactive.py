#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AdsPower Browser交互式自动化脚本
用于自动化操作AdsPower Browser软件 - 交互式版本
"""

import pyautogui as pg
import time
import sys

# 禁用pyautogui的安全机制
pg.FAILSAFE = False
pg.PAUSE = 0.3

class AdsPowerInteractiveAutomation:
    """AdsPower Browser交互式自动化操作类"""
    
    def __init__(self):
        """初始化自动化操作类"""
        print("AdsPower Browser 交互式自动化脚本")
        print("=" * 50)
        print("请确保AdsPower Browser已经打开并可见")
        print("=" * 50)
    
    def wait_for_user_confirmation(self, message: str) -> bool:
        """等待用户确认"""
        print(f"\n{message}")
        response = input("按回车键继续，或输入 'q' 退出: ").strip().lower()
        return response != 'q'
    
    def click_with_confirmation(self, x: int, y: int, description: str) -> bool:
        """带确认的点击操作"""
        print(f"\n准备{description}")
        print(f"目标坐标: ({x}, {y})")
        
        if not self.wait_for_user_confirmation(f"即将{description}，请确认AdsPower窗口已打开且可见"):
            return False
        
        try:
            # 移动鼠标到目标位置
            pg.moveTo(x, y, duration=0.5)
            print(f"鼠标已移动到 ({x}, {y})")
            
            if not self.wait_for_user_confirmation("鼠标位置正确吗？按回车确认点击"):
                return False
            
            # 执行点击
            pg.click(x, y)
            print(f"✅ 已{description}")
            time.sleep(1)
            return True
            
        except Exception as e:
            print(f"❌ {description}时出错: {e}")
            return False
    
    def type_with_confirmation(self, text: str, description: str) -> bool:
        """带确认的文本输入操作"""
        print(f"\n准备{description}")
        print(f"要输入的文本: '{text}'")
        
        if not self.wait_for_user_confirmation(f"即将{description}，请确认光标在正确的输入框中"):
            return False
        
        try:
            # 清空现有内容
            pg.hotkey('ctrl', 'a')
            time.sleep(0.2)
            
            # 输入新文本
            pg.typewrite(text, interval=0.1)
            print(f"✅ 已{description}: '{text}'")
            time.sleep(0.5)
            return True
            
        except Exception as e:
            print(f"❌ {description}时出错: {e}")
            return False
    
    def automate_create_browser_interactive(self, browser_name: str = "1") -> bool:
        """交互式自动化创建浏览器的完整流程"""
        print("\n🚀 开始交互式自动化创建浏览器流程...")
        print("=" * 50)
        
        # 步骤1: 点击新建浏览器按钮
        print("\n📍 步骤1: 点击新建浏览器按钮")
        if not self.click_with_confirmation(102, 82, "点击新建浏览器按钮"):
            print("❌ 用户取消或点击新建浏览器按钮失败")
            return False
        
        # 等待对话框打开
        time.sleep(2)
        
        # 步骤2: 点击名称输入框并输入名称
        print("\n📍 步骤2: 输入浏览器名称")
        if not self.click_with_confirmation(563, 177, "点击名称输入框"):
            print("❌ 用户取消或点击名称输入框失败")
            return False
        
        if not self.type_with_confirmation(browser_name, f"输入浏览器名称"):
            print("❌ 用户取消或输入浏览器名称失败")
            return False
        
        # 步骤3: 点击确定按钮
        print("\n📍 步骤3: 点击确定按钮")
        if not self.click_with_confirmation(379, 603, "点击确定按钮"):
            print("❌ 用户取消或点击确定按钮失败")
            return False
        
        print("\n🎉 交互式自动化创建浏览器流程完成!")
        print("=" * 50)
        return True
    
    def show_mouse_position(self):
        """显示当前鼠标位置（调试用）"""
        print("\n🔍 鼠标位置监控模式")
        print("移动鼠标到目标位置，按 Ctrl+C 退出")
        print("-" * 30)
        
        try:
            while True:
                x, y = pg.position()
                print(f"\r当前鼠标位置: ({x}, {y})", end="", flush=True)
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n退出鼠标位置监控")

def main():
    """主函数"""
    automation = AdsPowerInteractiveAutomation()
    
    while True:
        print("\n请选择操作:")
        print("1. 执行自动化创建浏览器流程")
        print("2. 显示鼠标位置（调试用）")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            browser_name = input("请输入浏览器名称 (默认为 '1'): ").strip()
            if not browser_name:
                browser_name = "1"
            
            success = automation.automate_create_browser_interactive(browser_name)
            
            if success:
                print("\n✅ 所有操作已成功完成!")
            else:
                print("\n❌ 操作过程中出现错误或被用户取消")
                
        elif choice == "2":
            automation.show_mouse_position()
            
        elif choice == "3":
            print("再见!")
            break
            
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
