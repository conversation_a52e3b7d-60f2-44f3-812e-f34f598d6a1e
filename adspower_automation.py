#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AdsPower Browser自动化脚本
用于自动化操作AdsPower Browser软件
"""

import pyautogui as pg
import uiautomation as ua
import time
import sys
from typing import Tuple, Optional

# 禁用pyautogui的安全机制
pg.FAILSAFE = False
pg.PAUSE = 0.5

class AdsPowerAutomation:
    """AdsPower Browser自动化操作类"""
    
    def __init__(self):
        """初始化自动化操作类"""
        self.window = None
        
    def find_adspower_window(self) -> Optional[ua.Control]:
        """查找AdsPower Browser窗口"""
        try:
            # 查找AdsPower窗口
            windows = ua.GetRootControl().GetChildren()
            for window in windows:
                window_name = window.Name.lower() if window.Name else ""
                if any(keyword in window_name for keyword in ["adspower", "ads power", "环境管理", "新建浏览器"]):
                    print(f"找到可能的AdsPower窗口: {window.Name}")
                    return window

            # 如果没找到，尝试查找包含相关关键词的窗口
            for window in windows:
                if window.Name and len(window.Name.strip()) > 0:
                    print(f"检查窗口: {window.Name}")

            return None
        except Exception as e:
            print(f"查找AdsPower窗口时出错: {e}")
            return None
    
    def get_desktop_state(self) -> str:
        """获取桌面状态信息"""
        try:
            desktop = ua.GetRootControl()
            elements = desktop.GetChildren()
            
            state_info = "当前桌面状态:\n"
            state_info += "=" * 50 + "\n"
            
            for i, element in enumerate(elements):
                if element.Name and element.Name.strip():
                    rect = element.BoundingRectangle
                    state_info += f"{i+1}. 窗口: {element.Name}\n"
                    state_info += f"   类型: {element.ControlTypeName}\n"
                    state_info += f"   位置: ({rect.left}, {rect.top}, {rect.right}, {rect.bottom})\n"
                    state_info += f"   可见: {element.IsEnabled}\n\n"
            
            return state_info
        except Exception as e:
            return f"获取桌面状态时出错: {e}"
    
    def find_interactive_elements(self, window: ua.Control) -> list:
        """查找窗口中的交互元素"""
        interactive_elements = []
        
        def find_elements_recursive(control: ua.Control, depth: int = 0):
            if depth > 10:  # 限制递归深度
                return
                
            try:
                # 查找按钮、文本框等交互元素
                if control.ControlType in [
                    ua.ControlType.ButtonControl,
                    ua.ControlType.EditControl,
                    ua.ControlType.TextControl,
                    ua.ControlType.ComboBoxControl,
                    ua.ControlType.CheckBoxControl
                ]:
                    rect = control.BoundingRectangle
                    if rect.width() > 0 and rect.height() > 0:
                        interactive_elements.append({
                            'name': control.Name or control.AutomationId or f"未命名{control.ControlTypeName}",
                            'type': control.ControlTypeName,
                            'rect': rect,
                            'center': (rect.left + rect.width()//2, rect.top + rect.height()//2),
                            'control': control
                        })
                
                # 递归查找子元素
                for child in control.GetChildren():
                    find_elements_recursive(child, depth + 1)
                    
            except Exception as e:
                pass  # 忽略单个元素的错误
        
        find_elements_recursive(window)
        return interactive_elements
    
    def click_new_browser_button(self) -> bool:
        """点击"新建浏览器"按钮"""
        try:
            # 首先尝试查找AdsPower窗口
            window = self.find_adspower_window()
            if window:
                elements = self.find_interactive_elements(window)

                # 查找"新建浏览器"按钮
                for element in elements:
                    element_name = element['name'].lower() if element['name'] else ""
                    if ("新建" in element_name and "浏览器" in element_name) or "新建浏览器" in element_name:
                        print(f"找到按钮: {element['name']} 位置: {element['center']}")
                        pg.click(element['center'][0], element['center'][1])
                        time.sleep(1)
                        return True

            # 如果没找到窗口或按钮，提示用户手动操作
            print("=" * 50)
            print("未能自动找到AdsPower窗口或新建浏览器按钮")
            print("请确保AdsPower Browser已经打开并显示在屏幕上")
            print("将在3秒后尝试使用预设坐标点击...")
            print("=" * 50)

            time.sleep(3)

            # 根据您的截图，新建浏览器按钮大约在左上角
            print("尝试点击预设坐标位置的新建浏览器按钮...")
            pg.click(102, 82)  # 这是一个估计坐标
            time.sleep(1)
            return True

        except Exception as e:
            print(f"点击新建浏览器按钮时出错: {e}")
            return False
    
    def input_browser_name(self, name: str = "1") -> bool:
        """在名称字段输入指定名称"""
        try:
            time.sleep(1)  # 等待对话框打开
            
            # 查找当前活动窗口
            window = ua.GetForegroundControl()
            elements = self.find_interactive_elements(window)
            
            # 查找名称输入框
            name_input = None
            for element in elements:
                if element['type'] == 'EditControl' and ('名称' in element['name'] or '名' in element['name']):
                    name_input = element
                    break
            
            if name_input:
                print(f"找到名称输入框: {name_input['name']} 位置: {name_input['center']}")
                pg.click(name_input['center'][0], name_input['center'][1])
                time.sleep(0.5)
                
                # 清空现有内容并输入新名称
                pg.hotkey('ctrl', 'a')
                pg.typewrite(name, interval=0.1)
                return True
            else:
                # 如果没找到，尝试使用固定坐标
                print("未找到名称输入框，尝试使用固定坐标...")
                pg.click(563, 177)  # 根据截图估计的坐标
                time.sleep(0.5)
                pg.hotkey('ctrl', 'a')
                pg.typewrite(name, interval=0.1)
                return True
                
        except Exception as e:
            print(f"输入浏览器名称时出错: {e}")
            return False
    
    def click_confirm_button(self) -> bool:
        """点击确定按钮"""
        try:
            time.sleep(0.5)
            
            # 查找当前活动窗口
            window = ua.GetForegroundControl()
            elements = self.find_interactive_elements(window)
            
            # 查找确定按钮
            for element in elements:
                if "确定" in element['name'] or "确认" in element['name'] or "OK" in element['name']:
                    print(f"找到确定按钮: {element['name']} 位置: {element['center']}")
                    pg.click(element['center'][0], element['center'][1])
                    return True
            
            # 如果没找到，尝试使用固定坐标
            print("未找到确定按钮，尝试使用固定坐标...")
            pg.click(379, 603)  # 根据截图估计的坐标
            return True
            
        except Exception as e:
            print(f"点击确定按钮时出错: {e}")
            return False
    
    def automate_create_browser(self, browser_name: str = "1") -> bool:
        """自动化创建浏览器的完整流程"""
        print("开始自动化创建浏览器流程...")
        
        # 步骤1: 点击新建浏览器按钮
        print("步骤1: 点击新建浏览器按钮")
        if not self.click_new_browser_button():
            print("❌ 点击新建浏览器按钮失败")
            return False
        print("✅ 成功点击新建浏览器按钮")
        
        # 步骤2: 输入浏览器名称
        print(f"步骤2: 输入浏览器名称 '{browser_name}'")
        if not self.input_browser_name(browser_name):
            print("❌ 输入浏览器名称失败")
            return False
        print("✅ 成功输入浏览器名称")
        
        # 步骤3: 点击确定按钮
        print("步骤3: 点击确定按钮")
        if not self.click_confirm_button():
            print("❌ 点击确定按钮失败")
            return False
        print("✅ 成功点击确定按钮")
        
        print("🎉 自动化创建浏览器流程完成!")
        return True

def main():
    """主函数"""
    print("AdsPower Browser 自动化脚本")
    print("=" * 40)
    
    automation = AdsPowerAutomation()
    
    # 显示当前桌面状态
    print("获取桌面状态...")
    desktop_state = automation.get_desktop_state()
    print(desktop_state)
    
    # 执行自动化操作
    success = automation.automate_create_browser("1")
    
    if success:
        print("\n✅ 所有操作已成功完成!")
    else:
        print("\n❌ 操作过程中出现错误")

if __name__ == "__main__":
    main()
