#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AdsPower Browser启动器和自动化脚本
简化版本 - 通过Windows搜索启动AdsPower
"""

import pyautogui as pg
import time
import subprocess

# 禁用pyautogui的安全机制
pg.FAILSAFE = False
pg.PAUSE = 0.5

class AdsPowerLauncher:
    """AdsPower Browser启动器和自动化操作类"""
    
    def __init__(self):
        """初始化"""
        print("AdsPower Browser 启动器和自动化工具")
        print("=" * 50)
    
    def launch_adspower_via_search(self) -> bool:
        """通过Windows搜索启动AdsPower"""
        print("🚀 步骤1: 通过Windows搜索启动AdsPower Browser")
        print("=" * 50)
        
        try:
            print("1. 打开Windows开始菜单...")
            pg.press('win')
            time.sleep(1)
            
            print("2. 搜索AdsPower...")
            pg.typewrite('AdsPower', interval=0.1)
            time.sleep(2)
            
            print("3. 按回车启动...")
            pg.press('enter')
            time.sleep(1)
            
            print("✅ 已通过Windows搜索启动AdsPower")
            print("⏳ 等待AdsPower加载...")
            
            # 等待应用程序启动和加载
            for i in range(10, 0, -1):
                print(f"   等待 {i} 秒...")
                time.sleep(1)
            
            print("✅ AdsPower应该已经准备就绪")
            return True
            
        except Exception as e:
            print(f"❌ 启动AdsPower失败: {e}")
            return False
    
    def launch_adspower_manual(self) -> bool:
        """手动启动AdsPower的指导"""
        print("🚀 步骤1: 手动启动AdsPower Browser")
        print("=" * 50)
        print("请手动执行以下操作:")
        print("1. 点击Windows开始菜单")
        print("2. 搜索'AdsPower'或'AdsPower Browser'")
        print("3. 点击AdsPower应用程序启动")
        print("4. 等待AdsPower完全加载")
        print("=" * 50)
        
        input("完成上述操作后，按回车键继续...")
        return True
    
    def create_new_browser(self, browser_name: str = "1") -> bool:
        """创建新浏览器的完整流程"""
        print(f"\n🎯 步骤2-4: 创建新浏览器 '{browser_name}'")
        print("=" * 50)
        
        try:
            # 步骤2: 点击新建浏览器按钮
            print("📍 点击新建浏览器按钮...")
            pg.click(102, 82)
            print("✅ 已点击新建浏览器按钮")
            time.sleep(2)
            
            # 步骤3: 输入浏览器名称
            print(f"📍 输入浏览器名称 '{browser_name}'...")
            pg.click(563, 177)  # 点击名称输入框
            time.sleep(0.5)
            pg.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.2)
            pg.typewrite(browser_name, interval=0.1)  # 输入名称
            print(f"✅ 已输入浏览器名称: '{browser_name}'")
            time.sleep(0.5)
            
            # 步骤4: 点击确定按钮
            print("📍 点击确定按钮...")
            pg.click(379, 603)
            print("✅ 已点击确定按钮")
            
            print("\n🎉 新建浏览器操作完成!")
            return True
            
        except Exception as e:
            print(f"❌ 创建新浏览器失败: {e}")
            return False
    
    def complete_workflow_auto(self, browser_name: str = "1") -> bool:
        """自动化完整工作流程"""
        print("🎯 自动化完整工作流程")
        print("=" * 60)
        
        # 给用户准备时间
        for i in range(3, 0, -1):
            print(f"⏰ {i} 秒后开始...")
            time.sleep(1)
        
        # 步骤1: 启动AdsPower
        if not self.launch_adspower_via_search():
            print("❌ 自动启动失败")
            return False
        
        # 步骤2-4: 创建新浏览器
        if not self.create_new_browser(browser_name):
            print("❌ 创建新浏览器失败")
            return False
        
        print("\n🎉 完整工作流程执行成功!")
        return True
    
    def complete_workflow_manual(self, browser_name: str = "1") -> bool:
        """手动启动 + 自动化后续操作的工作流程"""
        print("🎯 手动启动 + 自动化操作工作流程")
        print("=" * 60)
        
        # 步骤1: 手动启动AdsPower
        if not self.launch_adspower_manual():
            return False
        
        # 步骤2-4: 自动化创建新浏览器
        if not self.create_new_browser(browser_name):
            print("❌ 创建新浏览器失败")
            return False
        
        print("\n🎉 完整工作流程执行成功!")
        return True

def main():
    """主函数"""
    launcher = AdsPowerLauncher()
    
    while True:
        print("\n请选择执行方式:")
        print("1. 完全自动化 (自动启动AdsPower + 自动创建浏览器)")
        print("2. 半自动化 (手动启动AdsPower + 自动创建浏览器)")
        print("3. 仅自动创建浏览器 (假设AdsPower已运行)")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            browser_name = input("请输入浏览器名称 (默认为 '1'): ").strip()
            if not browser_name:
                browser_name = "1"
            launcher.complete_workflow_auto(browser_name)
            
        elif choice == "2":
            browser_name = input("请输入浏览器名称 (默认为 '1'): ").strip()
            if not browser_name:
                browser_name = "1"
            launcher.complete_workflow_manual(browser_name)
            
        elif choice == "3":
            browser_name = input("请输入浏览器名称 (默认为 '1'): ").strip()
            if not browser_name:
                browser_name = "1"
            
            print("⚠️  请确保AdsPower Browser已经运行并显示在屏幕上")
            input("确认后按回车键继续...")
            
            if launcher.create_new_browser(browser_name):
                print("✅ 操作成功完成!")
            else:
                print("❌ 操作失败")
                
        elif choice == "4":
            print("再见!")
            break
            
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
