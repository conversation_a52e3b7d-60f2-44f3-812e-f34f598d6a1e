#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的AdsPower Browser自动化脚本
1. 启动AdsPower Browser软件
2. 点击新建浏览器
3. 输入名称"1"
4. 点击确定
"""

import pyautogui as pg
import subprocess
import time
import os
import sys
from pathlib import Path

# 禁用pyautogui的安全机制
pg.FAILSAFE = False
pg.PAUSE = 0.5

class CompleteAdsPowerAutomation:
    """完整的AdsPower Browser自动化操作类"""
    
    def __init__(self):
        """初始化自动化操作类"""
        self.adspower_paths = [
            # 常见的AdsPower安装路径
            r"C:\Program Files\AdsPower\AdsPower.exe",
            r"C:\Program Files (x86)\AdsPower\AdsPower.exe",
            r"C:\Users\<USER>\AppData\Local\AdsPower\AdsPower.exe".format(os.getenv('USERNAME')),
            r"C:\Users\<USER>\AppData\Roaming\AdsPower\AdsPower.exe".format(os.getenv('USERNAME')),
            # 桌面快捷方式
            r"C:\Users\<USER>\Desktop\AdsPower.lnk".format(os.getenv('USERNAME')),
            r"C:\Users\<USER>\Desktop\AdsPower.lnk",
        ]
    
    def find_adspower_executable(self) -> str:
        """查找AdsPower可执行文件"""
        print("🔍 正在查找AdsPower Browser安装路径...")
        
        for path in self.adspower_paths:
            if os.path.exists(path):
                print(f"✅ 找到AdsPower: {path}")
                return path
        
        # 如果没找到，尝试在常见目录搜索
        search_dirs = [
            r"C:\Program Files",
            r"C:\Program Files (x86)",
            f"C:\\Users\\<USER>\\AppData\\Local",
            f"C:\\Users\\<USER>\\AppData\\Roaming"
        ]
        
        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                for root, dirs, files in os.walk(search_dir):
                    for file in files:
                        if file.lower() in ['adspower.exe', 'adspower browser.exe']:
                            full_path = os.path.join(root, file)
                            print(f"✅ 搜索找到AdsPower: {full_path}")
                            return full_path
        
        return None
    
    def launch_adspower(self) -> bool:
        """启动AdsPower Browser"""
        print("🚀 步骤1: 启动AdsPower Browser软件")
        print("=" * 50)
        
        # 方法1: 尝试通过开始菜单启动
        try:
            print("尝试通过开始菜单启动AdsPower...")
            subprocess.run(['powershell', '-Command', 'Start-Process', 'AdsPower'], 
                         check=True, capture_output=True)
            print("✅ 通过开始菜单成功启动AdsPower")
            time.sleep(5)  # 等待应用程序启动
            return True
        except subprocess.CalledProcessError:
            print("❌ 通过开始菜单启动失败")
        
        # 方法2: 查找并直接启动可执行文件
        adspower_path = self.find_adspower_executable()
        if adspower_path:
            try:
                print(f"尝试直接启动: {adspower_path}")
                subprocess.Popen([adspower_path])
                print("✅ 直接启动AdsPower成功")
                time.sleep(5)  # 等待应用程序启动
                return True
            except Exception as e:
                print(f"❌ 直接启动失败: {e}")
        
        # 方法3: 尝试通过Windows搜索启动
        try:
            print("尝试通过Windows搜索启动...")
            # 打开开始菜单并搜索
            pg.press('win')
            time.sleep(1)
            pg.typewrite('AdsPower', interval=0.1)
            time.sleep(2)
            pg.press('enter')
            print("✅ 通过Windows搜索启动AdsPower")
            time.sleep(5)  # 等待应用程序启动
            return True
        except Exception as e:
            print(f"❌ 通过Windows搜索启动失败: {e}")
        
        print("❌ 所有启动方法都失败了")
        return False
    
    def wait_for_adspower_ready(self) -> bool:
        """等待AdsPower Browser准备就绪"""
        print("\n⏳ 等待AdsPower Browser完全加载...")
        
        max_wait_time = 30  # 最大等待30秒
        wait_interval = 2   # 每2秒检查一次
        
        for i in range(0, max_wait_time, wait_interval):
            print(f"等待中... ({i}/{max_wait_time}秒)")
            time.sleep(wait_interval)
            
            # 这里可以添加检查AdsPower是否已经加载完成的逻辑
            # 比如检查特定窗口标题或UI元素
            
        print("✅ AdsPower Browser应该已经准备就绪")
        return True
    
    def click_new_browser_button(self) -> bool:
        """点击新建浏览器按钮"""
        print("\n📍 步骤2: 点击新建浏览器按钮")
        print("=" * 50)
        
        try:
            # 根据您提供的截图坐标
            x, y = 102, 82
            print(f"点击坐标: ({x}, {y})")
            pg.click(x, y)
            print("✅ 已点击新建浏览器按钮")
            time.sleep(2)  # 等待对话框打开
            return True
        except Exception as e:
            print(f"❌ 点击新建浏览器按钮失败: {e}")
            return False
    
    def input_browser_name(self, name: str = "1") -> bool:
        """输入浏览器名称"""
        print(f"\n📍 步骤3: 输入浏览器名称 '{name}'")
        print("=" * 50)
        
        try:
            # 点击名称输入框
            x, y = 563, 177
            print(f"点击名称输入框坐标: ({x}, {y})")
            pg.click(x, y)
            time.sleep(0.5)
            
            # 清空现有内容并输入新名称
            pg.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pg.typewrite(name, interval=0.1)
            print(f"✅ 已输入浏览器名称: '{name}'")
            time.sleep(0.5)
            return True
        except Exception as e:
            print(f"❌ 输入浏览器名称失败: {e}")
            return False
    
    def click_confirm_button(self) -> bool:
        """点击确定按钮"""
        print("\n📍 步骤4: 点击确定按钮")
        print("=" * 50)
        
        try:
            # 根据您提供的截图坐标
            x, y = 379, 603
            print(f"点击确定按钮坐标: ({x}, {y})")
            pg.click(x, y)
            print("✅ 已点击确定按钮")
            time.sleep(1)
            return True
        except Exception as e:
            print(f"❌ 点击确定按钮失败: {e}")
            return False
    
    def complete_automation(self, browser_name: str = "1") -> bool:
        """执行完整的自动化流程"""
        print("🎯 AdsPower Browser 完整自动化流程")
        print("=" * 60)
        print("即将执行以下步骤:")
        print("1. 启动AdsPower Browser软件")
        print("2. 点击新建浏览器按钮")
        print(f"3. 输入浏览器名称: '{browser_name}'")
        print("4. 点击确定按钮")
        print("=" * 60)
        
        # 给用户5秒准备时间
        for i in range(5, 0, -1):
            print(f"⏰ {i} 秒后开始执行...")
            time.sleep(1)
        
        print("\n🚀 开始执行自动化流程!")
        
        # 步骤1: 启动AdsPower Browser
        if not self.launch_adspower():
            print("❌ 启动AdsPower Browser失败")
            return False
        
        # 等待应用程序准备就绪
        if not self.wait_for_adspower_ready():
            print("❌ AdsPower Browser未能准备就绪")
            return False
        
        # 步骤2: 点击新建浏览器按钮
        if not self.click_new_browser_button():
            print("❌ 点击新建浏览器按钮失败")
            return False
        
        # 步骤3: 输入浏览器名称
        if not self.input_browser_name(browser_name):
            print("❌ 输入浏览器名称失败")
            return False
        
        # 步骤4: 点击确定按钮
        if not self.click_confirm_button():
            print("❌ 点击确定按钮失败")
            return False
        
        print("\n🎉 完整自动化流程执行成功!")
        print("=" * 60)
        return True

def main():
    """主函数"""
    automation = CompleteAdsPowerAutomation()
    
    print("AdsPower Browser 完整自动化工具")
    print("=" * 50)
    
    browser_name = input("请输入要创建的浏览器名称 (默认为 '1'): ").strip()
    if not browser_name:
        browser_name = "1"
    
    success = automation.complete_automation(browser_name)
    
    if success:
        print("\n✅ 所有操作已成功完成!")
    else:
        print("\n❌ 操作过程中出现错误")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
